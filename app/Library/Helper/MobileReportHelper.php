<?php

namespace App\Library\Helper;

use App\Models\StudentInformation;
use App\Models\User;
use App\Models\Classroom;
use App\Models\StudentClassroom;
use App\Models\StudentAttendance;
use App\Models\SummativeAssessmentData;
use App\Models\FormativeAssessmentData;
use App\Models\DisciplineRecord;
use App\Models\Homework;
use App\Models\HomeworkDetail;
use App\Models\LibraryBorrowingRecord;
use App\Library\Helper\AcademicHelper;
use Carbon\Carbon;

class MobileReportHelper
{
    protected $ah;

    public function __construct()
    {
        $this->ah = new AcademicHelper();
    }

    /**
     * Format data for mobile consumption with optimized structure
     */
    public function formatForMobile($data, $type = 'default')
    {
        switch ($type) {
            case 'chart':
                return $this->formatChartData($data);
            case 'table':
                return $this->formatTableData($data);
            case 'summary':
                return $this->formatSummaryData($data);
            default:
                return $data;
        }
    }

    /**
     * Format chart data for mobile visualization
     */
    private function formatChartData($data)
    {
        return [
            'labels' => $data['labels'] ?? [],
            'datasets' => $data['datasets'] ?? [],
            'colors' => $data['colors'] ?? ['#3498db', '#e74c3c', '#2ecc71', '#f39c12'],
            'type' => $data['type'] ?? 'bar'
        ];
    }

    /**
     * Format table data for mobile display
     */
    private function formatTableData($data)
    {
        return [
            'headers' => $data['headers'] ?? [],
            'rows' => $data['rows'] ?? [],
            'total_rows' => count($data['rows'] ?? []),
            'pagination' => $data['pagination'] ?? null
        ];
    }

    /**
     * Format summary data for mobile cards
     */
    private function formatSummaryData($data)
    {
        return [
            'cards' => $data['cards'] ?? [],
            'total_cards' => count($data['cards'] ?? []),
            'last_updated' => now()->toISOString()
        ];
    }

    /**
     * Get student attendance analytics
     */
    public function getStudentAttendanceAnalytics($studentId, $branchId, $dateRange = null)
    {
        $academicYearId = $this->ah->branchAcademicYear($branchId);
        
        // Set date range (default to current academic year)
        if (!$dateRange) {
            // Get current semester start date as fallback
            $semesterInfo = \App\Models\AcademicSemester::where('is_default', 1)
                ->where('branch_id', $branchId)
                ->first();
            $startDate = $semesterInfo ? $semesterInfo->start_date : date('Y-m-01');
            $endDate = now()->format('Y-m-d');
        } else {
            $startDate = $dateRange['start'];
            $endDate = $dateRange['end'];
        }

        // Get attendance records
        $attendanceRecords = \App\Models\StudentAttendance::where('student_id', $studentId)
            ->whereBetween('date', [$startDate, $endDate])
            ->get();

        $totalDays = $attendanceRecords->count();
        $presentDays = $attendanceRecords->where('attendance_status', 'present')->count();
        $absentDays = $attendanceRecords->where('attendance_status', 'absent')->count();
        $lateDays = $attendanceRecords->where('attendance_status', 'late')->count();

        $attendanceRate = $totalDays > 0 ? round(($presentDays / $totalDays) * 100, 2) : 0;

        // Monthly breakdown
        $monthlyData = $attendanceRecords->groupBy(function($record) {
            return Carbon::parse($record->date)->format('Y-m');
        })->map(function($records, $month) {
            return [
                'month' => $month,
                'total' => $records->count(),
                'present' => $records->where('attendance_status', 'present')->count(),
                'absent' => $records->where('attendance_status', 'absent')->count(),
                'late' => $records->where('attendance_status', 'late')->count()
            ];
        })->values();

        return [
            'summary' => [
                'total_days' => $totalDays,
                'present_days' => $presentDays,
                'absent_days' => $absentDays,
                'late_days' => $lateDays,
                'attendance_rate' => $attendanceRate
            ],
            'chart_data' => [
                'labels' => ['Present', 'Absent', 'Late'],
                'datasets' => [
                    [
                        'data' => [$presentDays, $absentDays, $lateDays],
                        'backgroundColor' => ['#2ecc71', '#e74c3c', '#f39c12']
                    ]
                ],
                'type' => 'doughnut'
            ],
            'monthly_breakdown' => $monthlyData,
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];
    }

    /**
     * Get student grade analytics
     */
    public function getStudentGradeAnalytics($studentId, $branchId, $dateRange = null)
    {
        $academicYearId = $this->ah->branchAcademicYear($branchId);
        
        if (!$dateRange) {
            // Get current semester start date as fallback
            $semesterInfo = \App\Models\AcademicSemester::where('is_default', 1)
                ->where('branch_id', $branchId)
                ->first();
            $startDate = $semesterInfo ? $semesterInfo->start_date : date('Y-m-01');
            $endDate = now()->format('Y-m-d');
        } else {
            $startDate = $dateRange['start'];
            $endDate = $dateRange['end'];
        }

        // Get summative assessments
        $summativeAssessments = SummativeAssessmentData::leftJoin('academic_summative_assessments', 
                'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
            ->leftJoin('subjects', 'subjects.subject_id', 'academic_summative_assessments.subject_id')
            ->where('academic_summative_assessments_data.student_id', $studentId)
            ->whereBetween('academic_summative_assessments.date', [$startDate, $endDate])
            ->select([
                'academic_summative_assessments_data.*',
                'academic_summative_assessments.subject_id',
                'academic_summative_assessments.date',
                'academic_summative_assessments.assessment_name',
                'subjects.subject_name'
            ])
            ->get();

        // Get formative assessments
        $formativeAssessments = FormativeAssessmentData::leftJoin('academic_formative_assessments',
                'academic_formative_assessments.assessment_id', 'academic_formative_assessments_data.assessment_id')
            ->leftJoin('subjects', 'subjects.subject_id', 'academic_formative_assessments.subject_id')
            ->where('academic_formative_assessments_data.student_id', $studentId)
            ->whereBetween('academic_formative_assessments.date', [$startDate, $endDate])
            ->select([
                'academic_formative_assessments_data.*',
                'academic_formative_assessments.subject_id',
                'academic_formative_assessments.date',
                'academic_formative_assessments.assessment_name',
                'subjects.subject_name'
            ])
            ->get();

        // Calculate subject averages
        $subjectAverages = [];
        $allSubjects = $summativeAssessments->pluck('subject_name', 'subject_id')->unique();

        foreach ($allSubjects as $subjectId => $subjectName) {
            $subjectSummative = $summativeAssessments->where('subject_id', $subjectId);
            $subjectFormative = $formativeAssessments->where('subject_id', $subjectId);
            
            $summativeAvg = $subjectSummative->avg('marks') ?? 0;
            $formativeAvg = $subjectFormative->avg('marks') ?? 0;
            
            // Weighted average (70% summative, 30% formative)
            $overallAvg = ($summativeAvg * 0.7) + ($formativeAvg * 0.3);
            
            $subjectAverages[] = [
                'subject_id' => $subjectId,
                'subject_name' => $subjectName,
                'summative_average' => round($summativeAvg, 2),
                'formative_average' => round($formativeAvg, 2),
                'overall_average' => round($overallAvg, 2),
                'total_assessments' => $subjectSummative->count() + $subjectFormative->count()
            ];
        }

        // Overall statistics
        $overallAverage = collect($subjectAverages)->avg('overall_average');
        $highestGrade = collect($subjectAverages)->max('overall_average');
        $lowestGrade = collect($subjectAverages)->min('overall_average');

        return [
            'summary' => [
                'overall_average' => round($overallAverage, 2),
                'highest_grade' => round($highestGrade, 2),
                'lowest_grade' => round($lowestGrade, 2),
                'total_subjects' => count($subjectAverages),
                'total_assessments' => $summativeAssessments->count() + $formativeAssessments->count()
            ],
            'subject_breakdown' => $subjectAverages,
            'chart_data' => [
                'labels' => collect($subjectAverages)->pluck('subject_name')->toArray(),
                'datasets' => [
                    [
                        'label' => 'Overall Average',
                        'data' => collect($subjectAverages)->pluck('overall_average')->toArray(),
                        'backgroundColor' => '#3498db'
                    ]
                ],
                'type' => 'bar'
            ],
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];
    }

    /**
     * Get student BPS analytics
     */
    public function getStudentBpsAnalytics($studentId, $branchId, $dateRange = null)
    {
        $academicYearId = $this->ah->branchAcademicYear($branchId);
        
        if (!$dateRange) {
            // Get current semester start date as fallback
            $semesterInfo = \App\Models\AcademicSemester::where('is_default', 1)
                ->where('branch_id', $branchId)
                ->first();
            $startDate = $semesterInfo ? $semesterInfo->start_date : date('Y-m-01');
            $endDate = now()->format('Y-m-d');
        } else {
            $startDate = $dateRange['start'];
            $endDate = $dateRange['end'];
        }

        // Get BPS records
        $bpsRecords = DisciplineRecord::leftJoin('discipline_items', 'discipline_items.item_id', 'discipline_records.item_id')
            ->where('discipline_records.student_id', $studentId)
            ->where('discipline_records.branch_id', $branchId)
            ->whereBetween('discipline_records.date', [$startDate, $endDate])
            ->select([
                'discipline_records.*',
                'discipline_items.item_title',
                'discipline_items.item_type'
            ])
            ->get();

        $totalPoints = $bpsRecords->sum('item_point');
        $positivePoints = $bpsRecords->where('item_type', 'PRS')->sum('item_point');
        $negativePoints = $bpsRecords->where('item_type', 'DPS')->sum('item_point');

        // Monthly breakdown
        $monthlyData = $bpsRecords->groupBy(function($record) {
            return Carbon::parse($record->date)->format('Y-m');
        })->map(function($records, $month) {
            return [
                'month' => $month,
                'total_points' => $records->sum('item_point'),
                'positive_points' => $records->where('item_type', 'PRS')->sum('item_point'),
                'negative_points' => $records->where('item_type', 'DPS')->sum('item_point'),
                'total_records' => $records->count()
            ];
        })->values();

        // Top items
        $topItems = $bpsRecords->groupBy('item_title')->map(function($records, $title) {
            return [
                'item_title' => $title,
                'count' => $records->count(),
                'total_points' => $records->sum('item_point'),
                'type' => $records->first()->item_type
            ];
        })->sortByDesc('count')->take(5)->values();

        return [
            'summary' => [
                'total_points' => $totalPoints,
                'positive_points' => $positivePoints,
                'negative_points' => abs($negativePoints),
                'total_records' => $bpsRecords->count(),
                'net_points' => $positivePoints + $negativePoints
            ],
            'chart_data' => [
                'labels' => ['Positive Points', 'Negative Points'],
                'datasets' => [
                    [
                        'data' => [$positivePoints, abs($negativePoints)],
                        'backgroundColor' => ['#2ecc71', '#e74c3c']
                    ]
                ],
                'type' => 'doughnut'
            ],
            'monthly_breakdown' => $monthlyData,
            'top_items' => $topItems,
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];
    }

    /**
     * Get student homework analytics
     */
    public function getStudentHomeworkAnalytics($studentId, $branchId, $dateRange = null)
    {
        $academicYearId = $this->ah->branchAcademicYear($branchId);
        
        if (!$dateRange) {
            // Get current semester start date as fallback
            $semesterInfo = \App\Models\AcademicSemester::where('is_default', 1)
                ->where('branch_id', $branchId)
                ->first();
            $startDate = $semesterInfo ? $semesterInfo->start_date : date('Y-m-01');
            $endDate = now()->format('Y-m-d');
        } else {
            $startDate = $dateRange['start'];
            $endDate = $dateRange['end'];
        }

        // Get homework assignments
        $homeworkDetails = HomeworkDetail::leftJoin('academic_homework', 'academic_homework.homework_id', 'academic_homework_detail.homework_id')
            ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_homework.grade_id')
            ->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
            ->where('academic_homework_detail.student_id', $studentId)
            ->where('academic_homework.branch_id', $branchId)
            ->whereBetween('academic_homework.due_date', [$startDate, $endDate])
            ->where('academic_homework.homework_status', 1) // Active homework only
            ->select([
                'academic_homework_detail.*',
                'academic_homework.homework_title',
                'academic_homework.due_date',
                'academic_homework.created_at as assigned_date',
                'subjects.subject_name',
                'academic_elective_grade.grade_name'
            ])
            ->get();

        $totalHomework = $homeworkDetails->count();
        $completedHomework = $homeworkDetails->where('is_complete', true)->count();
        $pendingHomework = $totalHomework - $completedHomework;
        $completionRate = $totalHomework > 0 ? round(($completedHomework / $totalHomework) * 100, 2) : 0;

        // Subject breakdown
        $subjectBreakdown = $homeworkDetails->groupBy('subject_name')->map(function($records, $subject) {
            $total = $records->count();
            $completed = $records->where('is_complete', true)->count();
            return [
                'subject_name' => $subject,
                'total' => $total,
                'completed' => $completed,
                'pending' => $total - $completed,
                'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0
            ];
        })->values();

        return [
            'summary' => [
                'total_homework' => $totalHomework,
                'completed_homework' => $completedHomework,
                'pending_homework' => $pendingHomework,
                'completion_rate' => $completionRate
            ],
            'chart_data' => [
                'labels' => ['Completed', 'Pending'],
                'datasets' => [
                    [
                        'data' => [$completedHomework, $pendingHomework],
                        'backgroundColor' => ['#2ecc71', '#f39c12']
                    ]
                ],
                'type' => 'doughnut'
            ],
            'subject_breakdown' => $subjectBreakdown,
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];
    }

    /**
     * Get class attendance summary for teachers
     * Handles both classroom-based (homeroom) and grade-based (subject) attendance
     */
    public function getClassAttendanceSummary($classOrGradeId, $branchId, $dateRange = null, $isGradeId = null)
    {
        if (!$dateRange) {
            $startDate = date('Y-m-01'); // Current month start
            $endDate = date('Y-m-d'); // Today
        } else {
            $startDate = $dateRange['start'];
            $endDate = $dateRange['end'];
        }

        // Auto-detect if this is a grade ID by checking if it exists in elective grades
        if ($isGradeId === null) {
            $isGradeId = \App\Models\ElectiveGrade::where('grade_id', $classOrGradeId)->exists();
        }

        // Get students based on context
        if ($isGradeId) {
            // For subject classes, use elective grade students
            $students = \App\Models\ElectiveGradeStudent::leftJoin('student_information', 'student_information.id', 'academic_elective_grade_students.student_id')
                ->where('academic_elective_grade_students.grade_id', $classOrGradeId)
                ->select(['student_information.id', 'student_information.name'])
                ->get();
        } else {
            // For homeroom classes, use classroom students
            $students = \App\Models\StudentClassroom::leftJoin('student_information', 'student_information.id', 'students_classroom.student_id')
                ->where('students_classroom.classroom_id', $classOrGradeId)
                ->select(['student_information.id', 'student_information.name'])
                ->get();
        }

        $totalStudents = $students->count();
        
        // Get attendance data for the date range
        $attendanceData = \App\Models\StudentAttendance::whereIn('student_id', $students->pluck('id'))
            ->whereBetween('date', [$startDate, $endDate])
            ->get();

        $totalDays = $attendanceData->groupBy('date')->count();
        $presentCount = $attendanceData->where('attendance_status', 'present')->count();
        $absentCount = $attendanceData->where('attendance_status', 'absent')->count();
        $lateCount = $attendanceData->where('attendance_status', 'late')->count();

        $attendanceRate = ($totalStudents * $totalDays) > 0 ? 
            round(($presentCount / ($totalStudents * $totalDays)) * 100, 2) : 0;

        // Daily breakdown
        $dailyBreakdown = $attendanceData->groupBy('date')->map(function($records, $date) use ($totalStudents) {
            $present = $records->where('attendance_status', 'present')->count();
            $absent = $records->where('attendance_status', 'absent')->count();
            $late = $records->where('attendance_status', 'late')->count();
            
            return [
                'date' => $date,
                'present' => $present,
                'absent' => $absent,
                'late' => $late,
                'total_students' => $totalStudents,
                'attendance_rate' => $totalStudents > 0 ? round(($present / $totalStudents) * 100, 2) : 0
            ];
        })->values();

        return [
            'summary' => [
                'total_students' => $totalStudents,
                'total_days' => $totalDays,
                'present_count' => $presentCount,
                'absent_count' => $absentCount,
                'late_count' => $lateCount,
                'attendance_rate' => $attendanceRate
            ],
            'chart_data' => [
                'labels' => $dailyBreakdown->pluck('date')->toArray(),
                'datasets' => [
                    [
                        'label' => 'Attendance Rate',
                        'data' => $dailyBreakdown->pluck('attendance_rate')->toArray(),
                        'backgroundColor' => '#3498db'
                    ]
                ],
                'type' => 'line'
            ],
            'daily_breakdown' => $dailyBreakdown,
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];
    }

    /**
     * Get assessment analytics for a class
     * Handles both classroom-based and grade-based assessment tracking
     */
    public function getClassAssessmentAnalytics($classOrGradeId, $branchId, $subjectId = null, $dateRange = null, $isGradeId = null)
    {
        $academicYearId = $this->ah->branchAcademicYear($branchId);

        if (!$dateRange) {
            // Get current semester start date as fallback
            $semesterInfo = \App\Models\AcademicSemester::where('is_default', 1)
                ->where('branch_id', $branchId)
                ->first();
            $startDate = $semesterInfo ? $semesterInfo->start_date : date('Y-m-01');
            $endDate = now()->format('Y-m-d');
        } else {
            $startDate = $dateRange['start'];
            $endDate = $dateRange['end'];
        }

        // Auto-detect if this is a grade ID by checking if it exists in elective grades
        if ($isGradeId === null) {
            $isGradeId = \App\Models\ElectiveGrade::where('grade_id', $classOrGradeId)->exists();
        }

        // Get students based on context
        if ($isGradeId) {
            // For subject classes, use elective grade students
            $students = \App\Models\ElectiveGradeStudent::leftJoin('student_information', 'student_information.id', 'academic_elective_grade_students.student_id')
                ->where('academic_elective_grade_students.grade_id', $classOrGradeId)
                ->select(['student_information.id', 'student_information.name'])
                ->get();
        } else {
            // For homeroom classes, use classroom students
            $students = \App\Models\StudentClassroom::leftJoin('student_information', 'student_information.id', 'students_classroom.student_id')
                ->where('students_classroom.classroom_id', $classOrGradeId)
                ->select(['student_information.id', 'student_information.name'])
                ->get();
        }

        $studentIds = $students->pluck('id');

        // Build assessment query
        $summativeQuery = SummativeAssessmentData::leftJoin('academic_summative_assessments',
                'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
            ->leftJoin('subjects', 'subjects.subject_id', 'academic_summative_assessments.subject_id')
            ->whereIn('academic_summative_assessments_data.student_id', $studentIds)
            ->whereBetween('academic_summative_assessments.date', [$startDate, $endDate]);

        if ($subjectId) {
            $summativeQuery->where('academic_summative_assessments.subject_id', $subjectId);
        }

        $summativeAssessments = $summativeQuery->select([
            'academic_summative_assessments_data.*',
            'academic_summative_assessments.subject_id',
            'academic_summative_assessments.assessment_name',
            'subjects.subject_name'
        ])->get();

        // Calculate class statistics
        $classAverage = $summativeAssessments->avg('marks') ?? 0;
        $highestScore = $summativeAssessments->max('marks') ?? 0;
        $lowestScore = $summativeAssessments->min('marks') ?? 0;
        $totalAssessments = $summativeAssessments->groupBy('assessment_id')->count();

        // Grade distribution
        $gradeDistribution = [
            'A' => $summativeAssessments->where('marks', '>=', 90)->count(),
            'B' => $summativeAssessments->whereBetween('marks', [80, 89])->count(),
            'C' => $summativeAssessments->whereBetween('marks', [70, 79])->count(),
            'D' => $summativeAssessments->whereBetween('marks', [60, 69])->count(),
            'F' => $summativeAssessments->where('marks', '<', 60)->count()
        ];

        // Subject breakdown (if multiple subjects)
        $subjectBreakdown = $summativeAssessments->groupBy('subject_name')->map(function($records, $subject) {
            return [
                'subject_name' => $subject,
                'average' => round($records->avg('marks'), 2),
                'highest' => $records->max('marks'),
                'lowest' => $records->min('marks'),
                'total_assessments' => $records->groupBy('assessment_id')->count()
            ];
        })->values();

        return [
            'summary' => [
                'class_average' => round($classAverage, 2),
                'highest_score' => $highestScore,
                'lowest_score' => $lowestScore,
                'total_students' => $students->count(),
                'total_assessments' => $totalAssessments
            ],
            'grade_distribution' => $gradeDistribution,
            'chart_data' => [
                'labels' => ['A (90-100)', 'B (80-89)', 'C (70-79)', 'D (60-69)', 'F (<60)'],
                'datasets' => [
                    [
                        'data' => array_values($gradeDistribution),
                        'backgroundColor' => ['#2ecc71', '#3498db', '#f39c12', '#e67e22', '#e74c3c']
                    ]
                ],
                'type' => 'doughnut'
            ],
            'subject_breakdown' => $subjectBreakdown,
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];
    }

    /**
     * Get behavioral analytics for a class or section
     */
    public function getBehavioralAnalytics($classroomId = null, $branchId, $teacherId = null, $dateRange = null)
    {
        $academicYearId = $this->ah->branchAcademicYear($branchId);

        if (!$dateRange) {
            // Get current semester start date as fallback
            $semesterInfo = \App\Models\AcademicSemester::where('is_default', 1)
                ->where('branch_id', $branchId)
                ->first();
            $startDate = $semesterInfo ? $semesterInfo->start_date : date('Y-m-01');
            $endDate = now()->format('Y-m-d');
        } else {
            $startDate = $dateRange['start'];
            $endDate = $dateRange['end'];
        }

        // Build query based on scope
        $query = DisciplineRecord::leftJoin('discipline_items', 'discipline_items.item_id', 'discipline_records.item_id')
            ->leftJoin('student_information', 'student_information.id', 'discipline_records.student_id')
            ->where('discipline_records.branch_id', $branchId)
            ->whereBetween('discipline_records.date', [$startDate, $endDate]);

        if ($classroomId) {
            // Get students in specific classroom
            $studentIds = StudentClassroom::where('classroom_id', $classroomId)->pluck('student_id');
            $query->whereIn('discipline_records.student_id', $studentIds);
        }

        if ($teacherId) {
            $query->where('discipline_records.user_id', $teacherId);
        }

        $bpsRecords = $query->select([
            'discipline_records.*',
            'discipline_items.item_title',
            'discipline_items.item_type',
            'student_information.name as student_name'
        ])->get();

        $totalRecords = $bpsRecords->count();
        $positiveRecords = $bpsRecords->where('item_type', 'PRS')->count();
        $negativeRecords = $bpsRecords->where('item_type', 'DPS')->count();
        $totalPoints = $bpsRecords->sum('item_point');

        // Top behavioral items
        $topItems = $bpsRecords->groupBy('item_title')->map(function($records, $title) {
            return [
                'item_title' => $title,
                'count' => $records->count(),
                'total_points' => $records->sum('item_point'),
                'type' => $records->first()->item_type
            ];
        })->sortByDesc('count')->take(10)->values();

        // Monthly trend
        $monthlyTrend = $bpsRecords->groupBy(function($record) {
            return Carbon::parse($record->date)->format('Y-m');
        })->map(function($records, $month) {
            return [
                'month' => $month,
                'total_records' => $records->count(),
                'positive_records' => $records->where('item_type', 'PRS')->count(),
                'negative_records' => $records->where('item_type', 'DPS')->count(),
                'total_points' => $records->sum('item_point')
            ];
        })->values();

        // Student breakdown (top 10 students with most records)
        $studentBreakdown = $bpsRecords->groupBy('student_id')->map(function($records, $studentId) {
            $student = $records->first();
            return [
                'student_id' => $studentId,
                'student_name' => $student->student_name,
                'total_records' => $records->count(),
                'positive_records' => $records->where('item_type', 'PRS')->count(),
                'negative_records' => $records->where('item_type', 'DPS')->count(),
                'total_points' => $records->sum('item_point')
            ];
        })->sortByDesc('total_records')->take(10)->values();

        return [
            'summary' => [
                'total_records' => $totalRecords,
                'positive_records' => $positiveRecords,
                'negative_records' => $negativeRecords,
                'total_points' => $totalPoints,
                'positive_percentage' => $totalRecords > 0 ? round(($positiveRecords / $totalRecords) * 100, 2) : 0
            ],
            'chart_data' => [
                'labels' => ['Positive Records', 'Negative Records'],
                'datasets' => [
                    [
                        'data' => [$positiveRecords, $negativeRecords],
                        'backgroundColor' => ['#2ecc71', '#e74c3c']
                    ]
                ],
                'type' => 'doughnut'
            ],
            'top_items' => $topItems,
            'monthly_trend' => $monthlyTrend,
            'student_breakdown' => $studentBreakdown,
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];
    }

    /**
     * Get homework completion analytics for teachers
     */
    public function getHomeworkCompletionAnalytics($gradeId, $branchId, $teacherId = null, $dateRange = null)
    {
        $academicYearId = $this->ah->branchAcademicYear($branchId);

        if (!$dateRange) {
            $startDate = date('Y-m-01'); // Current month start
            $endDate = date('Y-m-d'); // Today
        } else {
            $startDate = $dateRange['start'];
            $endDate = $dateRange['end'];
        }

        // Build homework query
        $query = Homework::leftJoin('academic_homework_detail', 'academic_homework_detail.homework_id', 'academic_homework.homework_id')
            ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_homework.grade_id')
            ->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
            ->where('academic_homework.branch_id', $branchId)
            ->where('academic_homework.grade_id', $gradeId)
            ->whereBetween('academic_homework.due_date', [$startDate, $endDate])
            ->where('academic_homework.homework_status', 1);

        if ($teacherId) {
            $query->where('academic_homework.created_by', $teacherId);
        }

        $homeworkData = $query->select([
            'academic_homework.*',
            'academic_homework_detail.student_id',
            'academic_homework_detail.is_complete',
            'academic_homework_detail.submitted_at',
            'subjects.subject_name'
        ])->get();

        $totalHomework = $homeworkData->groupBy('homework_id')->count();
        $totalSubmissions = $homeworkData->count();
        $completedSubmissions = $homeworkData->where('is_complete', true)->count();
        $completionRate = $totalSubmissions > 0 ? round(($completedSubmissions / $totalSubmissions) * 100, 2) : 0;

        // Subject breakdown
        $subjectBreakdown = $homeworkData->groupBy('subject_name')->map(function($records, $subject) {
            $total = $records->count();
            $completed = $records->where('is_complete', true)->count();
            return [
                'subject_name' => $subject,
                'total_assignments' => $records->groupBy('homework_id')->count(),
                'total_submissions' => $total,
                'completed_submissions' => $completed,
                'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0
            ];
        })->values();

        // Daily completion trend
        $dailyTrend = $homeworkData->where('is_complete', true)
            ->groupBy(function($record) {
                return Carbon::parse($record->submitted_at)->format('Y-m-d');
            })->map(function($records, $date) {
                return [
                    'date' => $date,
                    'completed_count' => $records->count()
                ];
            })->values();

        return [
            'summary' => [
                'total_homework_assigned' => $totalHomework,
                'total_submissions' => $totalSubmissions,
                'completed_submissions' => $completedSubmissions,
                'completion_rate' => $completionRate
            ],
            'chart_data' => [
                'labels' => ['Completed', 'Pending'],
                'datasets' => [
                    [
                        'data' => [$completedSubmissions, $totalSubmissions - $completedSubmissions],
                        'backgroundColor' => ['#2ecc71', '#f39c12']
                    ]
                ],
                'type' => 'doughnut'
            ],
            'subject_breakdown' => $subjectBreakdown,
            'daily_trend' => $dailyTrend,
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];
    }
}
